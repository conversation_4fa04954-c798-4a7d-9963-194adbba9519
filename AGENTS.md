# Nixtla Forecasting Project Configuration

## Project Overview

This is a research repository focused on advanced time series forecasting techniques, particularly for cryptocurrency price prediction using Nixtla's forecasting ecosystem. The repository contains research documents, Jupyter notebooks with various machine learning techniques, and experiments combining statistical models with deep learning approaches.

## Recent Session Context (May 21, 2025)

### Implementation Status
- Research directory reorganized for optimized AI context retrieval
- Created hierarchical structure with consistent naming conventions
- Added comprehensive metadata to all research files
- Created Research/CLAUDE.md as central navigation hub for research content

### Next Steps
1. Update documentation to reflect new organization
2. Ensure all cross-references are consistent with new structure
3. Begin implementation planning based on synthesis recommendations

## Recent Session Context (May 19, 2025)

### Implementation Status
- Modularized global configuration system completed
- Integrated MCP servers (Basic Memory and Software Planning)
- Research synthesis completed: `Synthesis/bitcoin_forecasting-complete-synthesis.md`
- Key divergences identified requiring resolution for planning phase

### Next Steps
1. Review synthesized document for planning/architecture phase
2. Resolve identified conflicts based on specific use case requirements
3. Begin implementation planning based on synthesis recommendations

## Key Technologies

- **Nixtla Ecosystem**: StatsForecast, NeuralForecast, and related tools for time series forecasting
- **Machine Learning Libraries**: PyTorch, TensorFlow/Keras, scikit-learn
- **Time Series Models**: ARIMA, LSTM, XGBoost, MSTL decomposition, and hybrid approaches
- **Technical Indicators**: 70+ indicators for financial analysis
- **Programming Language**: Python (Jupyter notebooks)

## Repository Structure

- `/Research/`: Research documents organized by domain and technique (see Research Directory Organization)
  - `/Research/CLAUDE.md`: Central navigation hub for all research content
- `/Research/Applications/`: Domain-specific implementations of forecasting techniques
- `/Research/Architecture/`: System design patterns and architectural considerations
- `/Research/Forecasting/`: Core forecasting methodologies and implementations
- `/Research/Models/`: Machine learning and statistical models
- `/Research/Synthesis/`: Comprehensive documents that synthesize multiple approaches
- `/Plans:Templates:Tools:Updates/`: Planning templates and utility tools
- `/modules/`: Global configuration modules (when working locally)

## Research Directory Organization

The Research directory has been reorganized to optimize for AI context retrieval with a consistent structure:

### Organization Principles
- Maximum directory depth of 3 levels for optimal navigation
- Consistent file naming convention: `primary-topic_specific-descriptor.extension`
- Comprehensive metadata for enhanced AI comprehension
- Topic-based organization with clear relationship mapping

### Key Research Documents
- Bitcoin Forecasting Synthesis: `/Research/Synthesis/bitcoin_forecasting-complete-synthesis.md`
- Modular Architecture Design: `/Research/Architecture/modular_hybrid-forecasting-architecture.md`
- Intraday Bitcoin Forecasting: `/Research/Applications/Bitcoin/intraday_forecasting-guide.md`
- Hybrid Integration Strategies: `/Research/Forecasting/Techniques/Integration/hybrid-integration_strategies-analysis.md`

### Research/CLAUDE.md
The dedicated Research CLAUDE.md file serves as a central navigation hub for all research content with:
- Complete directory structure
- File migration mapping
- Detailed metadata schema
- Search and retrieval strategies
- Content maintenance guidelines

Refer to `/Research/CLAUDE.md` for comprehensive navigation of research content.

## Project-Specific Guidelines

### Working with Jupyter Notebooks
- Use NotebookRead to view notebook contents
- Use NotebookEdit to modify specific cells
- Notebooks contain examples of various forecasting techniques

### Time Series Forecasting Patterns
- Use MSTL decomposition with periods [24, 168] for cryptocurrency data
- For GPU optimization, follow patterns in existing NeuralForecast notebooks
- Consider the 120-day hourly data constraint (~2880 data points)
- Focus on metrics like MAPE and R² for short-term (24-hour) forecasts

### Integration Points with Global Configuration
This project leverages the global MCP integration patterns for:
- Memory persistence of research findings and model results
- Planning and tracking of forecasting experiments
- Cross-session continuity for long-running analyses

## Research Focus Areas

1. Advanced decomposition techniques (STL, MSTL, X-13ARIMA-SEATS)
2. Feature engineering for high-frequency financial data
3. Ensemble methods combining multiple forecasting approaches
4. Cross-market modeling for multi-asset forecasting
5. Handling high volatility and low signal-to-noise ratio in crypto markets

## Performance Benchmarks
- PatchTST: 21% lower error than traditional models for intraday Bitcoin
- Dynamic ensemble weighting: 13-17% improvement over static approaches
- GPU acceleration required: 8GB+ VRAM for neural models