#!/usr/bin/env python3
"""
Title: Research File Migration Script
Date: 2025-05-20
Author: Claude
Category: Implementation/Organization
Tags: migration, organization, research, metadata
Summary: Script to migrate existing research files to the new directory structure with proper naming and metadata

Dependencies:
- os
- pathlib
- shutil
- re
- yaml
- datetime

Usage:
python migrate_research_files.py
"""

import os
import re
import shutil
import yaml
from datetime import datetime
from pathlib import Path

# Mapping dictionary for file migrations
# Format: 'original_path': ('new_directory', 'new_filename', 'category', ['tag1', 'tag2'])
FILE_MAPPING = {
    'Research/Nixtla_Bitcoin_Forecasting_Technical_Research.md': 
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '01_nixtla-bitcoin-technical-research.md', 
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'nixtla', 'technical-research']),
    
    'Research/Nixtla_Intraday_Bitcoin_Forecasting_Guide.md': 
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '02_intraday-bitcoin-forecasting-guide.md', 
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'intraday', 'guide']),
    
    'Research/nixtla_btc_forecasting.md': 
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '03_btc-forecasting-implementation.md', 
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'implementation']),
    
    'Research/nixtla_btc_forecasting_2.md': 
        ('02-Domain-Applications/Cryptocurrency/Bitcoin', '04_btc-forecasting-advanced.md', 
         'Domain-Applications/Cryptocurrency/Bitcoin', ['bitcoin', 'forecasting', 'advanced']),
    
    'Research/Methods:Techniques/Advanced Hybrid Integration Strategies for Time Series Forecasting.md': 
        ('03-Techniques/Ensembling', '01_hybrid-integration-strategies.md', 
         'Techniques/Ensembling', ['hybrid', 'integration', 'ensemble', 'time-series']),
    
    'Research/Methods:Techniques/Modular Architecture Design for Hybrid Time Series Forecasting Systems.md': 
        ('04-Implementation/Integration/System-Architecture', '01_modular-architecture-design.md', 
         'Implementation/Integration/System-Architecture', ['architecture', 'modular', 'design', 'forecasting-system']),
    
    'Research/Methods:Techniques/probabilistic-forecasting-using-gluonts-bitcoin.py': 
        ('04-Implementation/Code-Examples/Python', '01_probabilistic-forecasting-gluonts-bitcoin.py', 
         'Implementation/Code-Examples/Python', ['probabilistic', 'gluonts', 'bitcoin', 'python']),
    
    'Research/Methods:Techniques/time-series-decomposition-naive-example.py': 
        ('03-Techniques/Decomposition', '01_time-series-decomposition-example.py', 
         'Techniques/Decomposition', ['decomposition', 'time-series', 'example', 'python']),
    
    'Research/Methods:Techniques/Advanced Crypto Forecasting Techniques_.md': 
        ('02-Domain-Applications/Cryptocurrency/General', '01_advanced-crypto-forecasting-techniques.md', 
         'Domain-Applications/Cryptocurrency/General', ['cryptocurrency', 'forecasting', 'techniques', 'advanced']),
    
    'Research/Methods:Techniques/Advanced Techniques for Short-Term Cryptocurrency Price Forecasting.md': 
        ('02-Domain-Applications/Cryptocurrency/General', '02_short-term-cryptocurrency-price-forecasting.md', 
         'Domain-Applications/Cryptocurrency/General', ['cryptocurrency', 'short-term', 'price', 'forecasting']),
    
    'Research/Methods:Techniques/lstm-time-series-prediction-sine-wave-example.py': 
        ('04-Implementation/Code-Examples/Python', '02_lstm-time-series-sine-wave.py', 
         'Implementation/Code-Examples/Python', ['lstm', 'time-series', 'sine-wave', 'example']),
}

def extract_title_from_content(file_path):
    """Extract title from the first line of a file if it starts with # or similar"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if first_line.startswith('#'):
                return first_line.lstrip('#').strip()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    # If no title found, use the filename as title
    return Path(file_path).stem.replace('_', ' ').replace('-', ' ').title()

def create_markdown_frontmatter(file_path, category, tags):
    """Create YAML frontmatter for markdown files"""
    title = extract_title_from_content(file_path)
    today = datetime.now().strftime('%Y-%m-%d')
    
    frontmatter = {
        'title': title,
        'date': today,
        'last_updated': today,
        'author': 'Nixtla Team',
        'category': category,
        'tags': tags,
        'difficulty': 'intermediate',
        'models': [],
        'libraries': ['nixtla'],
        'related_documents': [],
        'status': 'migrated',
        'summary': f"Migrated document about {title}"
    }
    
    return yaml.dump(frontmatter, sort_keys=False, default_flow_style=False)

def create_python_header(file_path, category, tags):
    """Create header comment block for Python files"""
    title = Path(file_path).stem.replace('_', ' ').replace('-', ' ').title()
    today = datetime.now().strftime('%Y-%m-%d')
    
    header = f'''"""
Title: {title}
Date: {today}
Last Updated: {today}
Author: Nixtla Team
Category: {category}
Tags: {', '.join(tags)}
Difficulty: intermediate
Models: 
Libraries: nixtla
Related Documents: 
Status: migrated
Summary: Migrated script for {title}

Dependencies:
- numpy
- pandas
- matplotlib

Usage:
python {Path(file_path).name}
"""
'''
    return header

def migrate_file(project_path, orig_path, dest_info):
    """Migrate a single file to its new location with proper metadata"""
    dest_dir, dest_filename, category, tags = dest_info
    
    # Full paths
    source_path = project_path / orig_path
    target_dir = project_path / 'Research-New' / dest_dir
    target_path = target_dir / dest_filename
    
    # Ensure target directory exists
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # If source doesn't exist, skip
    if not source_path.exists():
        print(f"Source file not found: {source_path}")
        return
    
    print(f"Migrating: {source_path} -> {target_path}")
    
    # Read content
    try:
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {source_path}: {e}")
        return
    
    # Create appropriate metadata based on file type
    if source_path.suffix.lower() == '.md':
        # Create YAML frontmatter
        frontmatter = create_markdown_frontmatter(source_path, category, tags)
        new_content = f"---\n{frontmatter}---\n\n{content}"
    elif source_path.suffix.lower() == '.py':
        # Create Python header comment
        header = create_python_header(source_path, category, tags)
        
        # Check if file already has a docstring at the top
        if content.lstrip().startswith('"""') or content.lstrip().startswith("'''"):
            # Find the end of the existing docstring
            triple_quote = '"""' if content.lstrip().startswith('"""') else "'''"
            end_idx = content.find(triple_quote, content.find(triple_quote) + 3)
            if end_idx != -1:
                # Replace existing docstring
                content = content[:content.find(triple_quote)] + header + content[end_idx + 3:]
            else:
                new_content = header + content
        else:
            new_content = header + content
    else:
        # For other file types, just copy as is
        new_content = content
    
    # Write to new location
    try:
        with open(target_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"Successfully migrated: {target_path}")
    except Exception as e:
        print(f"Error writing {target_path}: {e}")

def migrate_unmapped_files(project_path):
    """Process files not explicitly mapped"""
    research_dir = project_path / 'Research'
    methods_dir = research_dir / 'Methods:Techniques'
    
    # Process Python files in Methods:Techniques
    for py_file in methods_dir.glob('*.py'):
        if str(py_file.relative_to(project_path)) not in FILE_MAPPING:
            # Check content to determine appropriate category
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            # Determine destination based on content
            if 'time series' in content or 'forecasting' in content:
                if 'decomposition' in content:
                    dest_dir = '03-Techniques/Decomposition'
                elif 'ensemble' in content or 'combined' in content:
                    dest_dir = '03-Techniques/Ensembling'
                else:
                    dest_dir = '04-Implementation/Code-Examples/Python'
            else:
                dest_dir = '04-Implementation/Code-Examples/Python'
            
            # Generate file number (find highest existing number and increment)
            target_dir = project_path / 'Research-New' / dest_dir
            existing_files = list(target_dir.glob('*.py'))
            file_num = len(existing_files) + 1
            
            # Create new filename
            new_filename = f"{file_num:02d}_{py_file.stem.lower().replace(' ', '-')}.py"
            
            # Define category and tags
            category = dest_dir.replace('/', '-')
            tags = [py_file.stem.lower().replace(' ', '-')]
            
            # Migrate file
            migrate_file(project_path, f"Research/Methods:Techniques/{py_file.name}", 
                        (dest_dir, new_filename, category, tags))

def update_index_files(project_path):
    """Update _index.md files with information about the directory contents"""
    research_dir = project_path / 'Research-New'
    
    for index_file in research_dir.glob('**/_index.md'):
        dir_path = index_file.parent
        
        # Get list of files in this directory (excluding _index.md)
        files = [f for f in dir_path.glob('*') if f.name != '_index.md' and not f.is_dir()]
        
        # Read current index content
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update content list
        content_list = "\n".join([f"- [{f.name}](./{f.name})" for f in files])
        if not content_list:
            content_list = "- (No content yet)"
        
        # Update the content list in the index file
        content = re.sub(r'## Contents\n\nThis directory contains the following:\n\n.*?\n\n',
                         f'## Contents\n\nThis directory contains the following:\n\n{content_list}\n\n',
                         content, flags=re.DOTALL)
        
        # Write updated content
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated index file: {index_file}")

def main():
    """Main function to run the migration process"""
    # Get project path
    project_path = Path(__file__).parent
    
    print(f"Starting migration process for {project_path}")
    
    # Process explicitly mapped files
    for orig_path, dest_info in FILE_MAPPING.items():
        migrate_file(project_path, orig_path, dest_info)
    
    # Process unmapped files
    migrate_unmapped_files(project_path)
    
    # Update index files
    update_index_files(project_path)
    
    print("\nMigration completed successfully!")
    print("Files have been migrated to the 'Research-New' directory.")
    print("Review the new structure and files before replacing the original 'Research' directory.")

if __name__ == "__main__":
    main()