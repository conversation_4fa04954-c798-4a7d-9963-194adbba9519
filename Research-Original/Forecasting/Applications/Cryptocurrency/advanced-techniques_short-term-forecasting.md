---
title: "Advanced Techniques for Short-Term Cryptocurrency Price Forecasting"
permalink: "forecasting/applications/cryptocurrency/advanced-techniques_short-term-forecasting"
type: "technical-report"
tags:
  - cryptocurrency
  - bitcoin
  - short-term-forecasting
  - time-series-decomposition
  - feature-engineering
  - ensemble-methods
  - regime-detection
  - prediction-intervals
models:
  - ARIMA
  - MSTL
  - GARCH
  - HMM
  - NGBoost
  - MarkovRegression
  - Theta
techniques:
  - time-series-decomposition
  - feature-engineering
  - ensemble-methods
  - hyperparameter-optimization
  - external-data-integration
  - regime-switching
  - prediction-interval-calibration
  - quantile-regression
summary: "Comprehensive analysis of eight advanced techniques to improve short-term (24-hour) Bitcoin price forecasts using hybrid statistical and machine learning approaches. Covers decomposition, feature engineering, ensembling, hyperparameter tuning, external data integration, regime detection, risk management, and prediction intervals."
related:
  - "forecasting/applications/bitcoin_forecasting-advanced"
  - "forecasting/techniques/decomposition_time-series-example"
  - "models/neural/lstm_sine-wave-example"
---

# Advanced Techniques for Short-Term Cryptocurrency Price Forecasting

## Introduction

Improving short-term Bitcoin price forecasts (e.g. BTC/USDT, 1-hour intervals) requires addressing the unique challenges of crypto time series: high volatility, multiple seasonal patterns, and a low signal-to-noise ratio. The current hybrid forecasting system combines Nixtla's StatsForecast models (AutoARIMA, AutoETS, SeasonalNaive) with deep learning, using \~120 days of hourly OHLC data (\~2880 points) and technical indicators. Performance issues – such as negative \$R^2\$ and MAPE > 22% – indicate that more advanced techniques are needed. Below, we explore eight key areas to enhance statistical forecasts in this setup, with practical examples and Python snippets. The emphasis is on methods feasible for \~120 days of hourly data and a 24-hour (24-step) forecast horizon.

## 1. Time Series Decomposition

Decomposing the time series into trend, seasonal, and residual components can improve model performance by isolating patterns. Advanced decomposition methods help capture **multiple seasonalities** (daily and weekly cycles) and **non-linear trends** common in crypto price data:

* **STL Decomposition (Seasonal-Trend with Loess):** STL is a robust method that iteratively extracts a smooth trend and a seasonal component using local regressions. It handles one seasonal period well (e.g. 24-hour daily seasonality) and can adapt to nonlinear trends via its LOESS smoothing. STL assumes the series can be additively decomposed \$Y\_t = T\_t + S\_t + I\_t\$ (trend + seasonal + irregular). In practice, STL can reveal daily cycles in crypto prices and a smoothed trend line even if the trend is not strictly linear. For example:

  ```python
  import pandas as pd
  from statsmodels.tsa.seasonal import STL

  y = btc_prices['Close']  # pandas Series of hourly BTC prices
  stl = STL(y, period=24, robust=True)  # 24h seasonality
  result = stl.fit()
  trend, seasonal, resid = result.trend, result.seasonal, result.resid
  ```

  This separates a 24-hour seasonal component and a LOESS trend. If the trend is highly non-linear (e.g. parabolic upswing or sudden shifts), STL's local smoothing will capture it better than a simple linear trend.

* **MSTL for Multiple Seasonalities:** Crypto data often show **multiple seasonal patterns** – e.g. a 24-hour daily cycle and a 168-hour weekly cycle (weekend vs weekday effects). *Multiple Seasonal-Trend decomposition using Loess (MSTL)* extends STL by allowing *multiple* seasonal components. It iteratively removes one seasonal frequency at a time. For example, one can specify daily and weekly periods to MSTL. In Nixtla's StatsForecast, MSTL is implemented as a model that decomposes the series and forecasts each component. It was introduced by Bandara et al. (2021) and forecasts each seasonal component with Seasonal Naïve while using a chosen model (e.g. ARIMA) for the trend. A StatsForecast example:

  ```python
  from statsforecast import StatsForecast
  from statsforecast.models import MSTL, AutoARIMA

  # Define MSTL model with daily (24h) and weekly (168h) seasonality
  models = [MSTL(season_length=[24, 168], trend_forecaster=AutoARIMA())]
  sf = StatsForecast(models=models, freq='H')
  sf.fit(df)  # df with columns ['unique_id','ds','y'] for hourly BTC price
  # Access decomposition:
  decomp = sf.fitted_[0, 0].model_  # DataFrame of components: ['data','trend','seasonal24','seasonal168','remainder']
  ```

  After fitting, `decomp` will contain columns for the 24h seasonal, 168h seasonal, trend, and remainder. This helps confirm, for instance, that there is a repeating intraday pattern and a weekly pattern in the residuals. The trend component (forecasted via ARIMA here) can capture slow non-linear drifts in price. Using MSTL, one could **forecast each component separately** (trend with ARIMA, daily and weekly seasonal with SeasonalNaive) and then recombine for the final forecast. This often yields better accuracy than a single model trying to handle all patterns.

* **X-13ARIMA-SEATS:** This is a classical seasonal adjustment method from the U.S. Census Bureau for decomposing time series. X-13ARIMA-SEATS fits an ARIMA model and applies iterative moving-average filters (X-11 algorithm) to estimate trend and seasonality. It is most commonly used for **monthly or quarterly data** (e.g. economic series) rather than hourly, as it expects a single seasonality (e.g. 12 for monthly). In a crypto context, X-13 could be used to **remove a dominant seasonal effect** (say 24h) by treating the 24-hour cycle akin to a "monthly" pattern in a daily indexed series, though it requires converting the data to a supported frequency. Given the complexity, X-13 is less convenient for hourly crypto data, but it demonstrates the principle of **additive decomposition** (\$Y\_t=T\_t+S\_t+I\_t\$) using moving averages and ARIMA modeling. Modern approaches like MSTL are more flexible for multiple seasons.

* **Other Advanced Methods:** In research, more exotic decompositions like *Singular Spectrum Analysis (SSA)*, *Wavelet transforms (DWT)*, or *Fourier analysis* can isolate cyclic patterns beyond daily/weekly. For example, SSA can extract oscillatory modes from crypto prices, and wavelet transforms can capture local frequency changes (useful if seasonal patterns evolve). These methods require care to avoid look-ahead bias (performing decomposition only on past data in a rolling manner). They can be useful if the crypto series has periodicities that are not strictly 24 or 168 hours (e.g. a 10-day cycle in investor behavior), though with 120 days of data one is limited in capturing very long cycles.

**Practical Guidance:** For 120 days of hourly BTC prices, it is highly effective to apply MSTL with periods \[24,168] to separate daily and weekly patterns. The decomposition can guide feature engineering (e.g. include the seasonal indices as features for a learned model) or serve in a hybrid approach: forecast the trend+season components with statistical models and let a machine learning model handle the complex residual. By removing known seasonality, subsequent models (like ARIMA or a neural net) can focus on predicting the residual variation, which often improves \$R^2\$. Always validate that the seasonal components make sense (e.g. plot the last few weeks of each component to ensure they are stable patterns). In summary, advanced decomposition helps acknowledge crypto's multi-period seasonality and non-linear trend, yielding cleaner inputs for forecasting models.

## 2. Advanced Feature Engineering

When using 70+ technical indicators and other exogenous data, careful feature engineering and selection is crucial. We need to create informative features (lags, rolling statistics) while reducing dimensionality and multicollinearity. Below are strategies:

* **Lag Features and Differencing:** Including lagged values of the target (and exogenous series) helps capture autoregressive patterns. For hourly BTC, recent lags (e.g. 1 hour, 2 hours, ... up to 24 hours) might be predictive of the next day's movement. It's common to take differences or returns as well to remove trend and stabilize variance. For example, to create lag features in pandas:

  ```python
  df['ret'] = df['Close'].pct_change()  # percent return as a feature
  for lag in [1, 2, 6, 24]:
      df[f'lag_{lag}'] = df['Close'].shift(lag)
  ```

  This yields features like last hour's price, last 6 hours' price, etc. Lags should be chosen based on domain knowledge (e.g. 24 may capture daily rebound patterns) or autocorrelation analysis (peaks in ACF indicate useful lags). Seasonal lags like 24 and 168 hours are particularly useful as features if not handling seasonality elsewhere. Differencing (or using returns) helps with non-stationarity; many crypto models use returns rather than raw price to model a more stationary series.

* **Rolling Statistics and Volatility Measures:** Capturing recent **momentum** and **volatility** can improve forecasts, especially in a volatile market like crypto. Rolling window features compress recent history: e.g. a 24-hour rolling mean (one-day average price) can act as a local trend, and a 24-hour rolling standard deviation as a volatility indicator. High volatility often implies larger forecast uncertainty or mean-reversion. Example:

  ```python
  df['roll_mean_24'] = df['Close'].rolling(window=24).mean()
  df['roll_std_24']  = df['Close'].rolling(window=24).std()  # 24h volatility
  df['roll_max_24']  = df['High'].rolling(24).max()  # recent high
  df['roll_min_24']  = df['Low'].rolling(24).min()   # recent low
  ```

  You can create similar 7-day rolling stats (168-hour window) to capture weekly trends. Other volatility measures include **ATR (Average True Range)** over the last N hours (which considers high-low range) and **GARCH-estimated volatility** (discussed later). Including realized volatility or ranges as features can help a model know if the market is turbulent or calm. For example, a sudden spike in 1h volatility might foreshadow larger price moves.

* **Technical Indicator Features:** The user has \~70 technical indicators (e.g. RSI, MACD, Bollinger Bands, etc.). These can provide signals of momentum, mean reversion, or overbought/oversold conditions. To avoid overwhelming the model, focus on **key indicators from diverse categories**: momentum (RSI, stochastic), trend (moving average crossovers, ADX), volume (OBV), volatility (Bollinger Band width, ATR) etc. Often, many indicators are redundant. For example, RSI and stochastic %K both measure momentum and will be highly correlated; multiple moving average combinations overlap. *Feature selection* is thus essential (see below). One strategy is to create all indicators but then use automated selection to find the most useful subset.

* **Feature Selection (PCA, Mutual Information, Boruta):** With dozens of features, selecting a relevant subset improves generalization. **Principal Component Analysis (PCA)** can reduce dimensionality by projecting indicators into a smaller set of uncorrelated components. For instance, you might find the first few principal components explain most variance across the 70 indicators, capturing common market movements. However, PCA components are linear combinations and not directly interpretable. Alternatively, filter methods like **mutual information** (MI) can rank features by non-linear relevance to the target. Using `sklearn.feature_selection.mutual_info_regression(X, y)` yields an MI score for each feature, indicating how much it reduces uncertainty in predicting the price move. This can highlight, say, that a volatility index or a particular moving average is highly informative for 24h returns.

  A robust wrapper approach is **Boruta feature selection**, which iteratively compares feature importance against shuffled shadow features to decide which are truly relevant. Boruta has been used in stock prediction to winnow technical indicators. One study started with 33 technical indicators and used Boruta to identify the most relevant ones, leading to significantly lower prediction error (MAE \~15 vs \~27) in a short-term stock forecast. In Python, Boruta can be applied with a random forest:

  ```python
  from boruta import BorutaPy
  from sklearn.ensemble import RandomForestRegressor

  X = df.drop(columns=['Close', 'ds', 'unique_id', 'y'])
  y = df['Close'].shift(-24)  # target: 24h ahead price (if doing direct forecast)
  model = RandomForestRegressor(n_estimators=1000, max_depth=5, random_state=0)
  boruta_selector = BorutaPy(model, n_estimators='auto', verbose=0, random_state=0)
  boruta_selector.fit(X.values, y.values)
  selected_feats = X.columns[boruta_selector.support_]
  ```

  This will zero in on features that have statistically significant importance. Often, a handful of technical indicators (like a specific oscillator or a volatility measure) will stand out as predictors of short-term crypto price changes. Those can be retained while others dropped to reduce noise.

* **Handling Multicollinearity:** Technical indicators often correlate with each other (e.g. many momentum indicators tell a similar story). High multicollinearity can degrade regression or ARIMAX models (unstable coefficient estimates) and inflate the feature space unnecessarily. To diagnose it, check the correlation matrix of features and compute Variance Inflation Factors (VIF). A simple approach is to remove one of any pair of features with correlation above a threshold (e.g. >0.9). For example:

  ```python
  import numpy as np
  corr = X.corr().abs()
  # Select upper triangle of correlation matrix
  upper = corr.where(np.triu(np.ones(corr.shape), k=1).astype(bool))
  # Find features with high correlation
  to_drop = [col for col in upper.columns if any(upper[col] > 0.95)]
  X_reduced = X.drop(columns=to_drop)
  ```

  This drops features that are 95%+ correlated with another. Another approach is PCA as mentioned (which inherently produces uncorrelated components). If using linear models, ridge or lasso regression can mitigate multicollinearity by shrinking coefficients; lasso will essentially select one among correlated features by setting others to zero. **Interaction** between features is another consideration – e.g. combining a price trend indicator with a sentiment indicator might be predictive – but given the short history, focus on main effects first to avoid overfitting.

In summary, crafting features for a crypto forecasting model should be guided by **domain intuition** (lags, technicals that matter in markets) and validated by **data-driven selection**. For 24-hour ahead BTC forecasts, a small set of well-chosen features often outperforms throwing all 70 into a model. We recommend: include recent lags and volatility; include a few technical indicators that capture different aspects of market state; and possibly include related assets (ETH price, if available, as an exogenous feature to capture market-wide moves). After feature creation, apply selection techniques (filter or Boruta) on a training window to pick the top features. This will combat the curse of dimensionality and help the models learn the true signal in this noisy dataset.

## 3. Model Selection and Ensembling

Choosing the right models (and combining them) is critical for volatile crypto data. No single model is always best – combining forecasts can **improve accuracy, reduce bias, and increase robustness**. Here we address model selection criteria specific to crypto and ensemble strategies for a hybrid system:

* **Model Selection for Crypto:** Traditional selection metrics like AIC/BIC (used in autoARIMA/ETS) ensure good in-sample fit, but for crypto, **out-of-sample performance** is paramount due to nonstationarity. Use rolling **backtests** or time-series cross validation to evaluate models on recent unseen data. For example, perform a sliding-window evaluation where you train on 100 days and test on the next 7 days, moving the window 7 days at a time. Evaluate error metrics like MAPE, sMAPE, or RMSE on each fold. This gives a realistic picture of which model family (ARIMA vs ETS vs Theta, etc.) handles the data best. Additionally, consider **crypto-specific criteria**: Does the model capture *directional accuracy* (how often it predicts the price will go up or down correctly)? In a trading context, a model that correctly predicts direction 60% of the time might be preferable even if its MAPE is slightly higher. Also, check performance during **high-volatility periods** vs **calm periods** – e.g. a model might perform well overall but completely miss predictions during sudden 10% swings. You might favor a model that is more robust during crashes even if slightly worse during normal times.

  When evaluating, also consider **Mean Absolute Scaled Error (MASE)** or **Median Absolute Error**, which are less sensitive to outliers than RMSE – important for crypto's occasional spikes. A negative \$R^2\$ indicates the model is worse than a naive mean predictor; this often happens if the model fails to capture the large variance in crypto prices. Incorporating a *naive benchmark* (like yesterday's price or a random walk forecast) is useful – the model should beat a 24h naive forecast (which is basically assuming no change or maybe repeating last day's pattern) to be considered effective. If SeasonalNaive (which repeats the price from 24 hours ago) is outperforming ARIMA, it suggests the seasonal pattern dominates and the more complex model isn't adding value – a sign to refine features or model complexity.

* **Ensembling Forecasts:** It's well-known in forecasting literature that combining forecasts from different models can yield improved accuracy and stability. Ensembles can be especially powerful in crypto where the data is noisy – different models may capture different aspects of the pattern, and averaging can cancel out some errors. Three common ensembling approaches are:

  * **Simple or Weighted Averaging:** Take the forecasts from multiple models (e.g. ARIMA, ETS, Prophet, a neural net) and average them. Weights can be equal or based on recent performance (e.g. weight inversely proportional to each model's error on a validation set). For instance, if ARIMA's 7-day MAPE is 20% and Prophet's is 25%, you might weight ARIMA 0.55 and Prophet 0.45. Weight selection can be done via optimization (solving for weights that minimize past combined error) or even a quick grid search. A simple weighted ensemble in code:

    ```python
    # Given two forecast arrays from different models:
    ens_forecast = 0.6 * forecast_arima + 0.4 * forecast_prophet
    ```

    Empirically, even an unweighted average of a few decent models often outperforms the best single model, by hedging against model-specific mistakes.

  * **Stacking (Meta-Learning):** Here a **meta-model** (e.g. a linear regression or XGBoost) is trained to combine the outputs of base models. For example, use ARIMA, ETS, and a neural net to generate training forecasts for each time point, then train a regression model that takes \[ARIMA\_pred, ETS\_pred, NN\_pred] as input features to predict the actual outcome. This meta-learner will learn an optimal combination (which could be non-linear) of the base forecasts. Care must be taken to avoid lookahead – typically stacking is done via cross-validation (to get out-of-sample predictions from base models for training the combiner). For instance:

    ```python
    import numpy as np
    from sklearn.linear_model import LinearRegression

    # Assume we have arrays: pred_arima, pred_prophet, pred_nn for the validation set
    X_meta = np.column_stack([pred_arima, pred_prophet, pred_nn])
    y_meta = actual_prices_val
    meta_model = LinearRegression().fit(X_meta, y_meta)
    # Later, for test:
    X_meta_test = np.column_stack([forecast_arima, forecast_prophet, forecast_nn])
    ensemble_pred = meta_model.predict(X_meta_test)
    ```

    Stacking can outperform simple averaging, but requires enough data to train the meta-model (with \~2880 points total, use stacking carefully to not overfit – perhaps use a simple model like linear regression or ridge as combiner).

  * **Bayesian Model Averaging (BMA):** This is a probabilistic ensemble where each model's forecast is weighted by its posterior probability given the data. In practice, one can approximate BMA by assigning weights to each model that sum to 1 (like averaging) but allow those weights to reflect uncertainty. For example, if two models have similar performance, BMA would assign similar weights, but if one clearly is better on recent data, BMA would concentrate weight there – yet still keep a bit of weight on the others in case the situation changes. Implementing full BMA involves Bayesian computations, but a simpler approach is to perform an expanding-window evaluation and update model weights over time (this is sometimes called dynamic model averaging). Essentially, continually recalibrate the ensemble weights as new data comes in, treating the weight as a distribution (with more variance when model performances are close). While full BMA is complex, the key idea for practitioners is **do not discard weaker models entirely**; use them in a smaller proportion to guard against regime changes (when the historically best model might falter).

* **Integrating StatsForecast, Prophet, Darts, etc.:** In a hybrid system, you may be using Nixtla's StatsForecast for fast statistical models, Facebook Prophet for its intuitive handling of seasonality/holidays, and perhaps Darts (a Python library) for additional models (Prophet and ARIMA are also accessible via Darts, as well as neural networks). Integration can happen via ensembling as above or via *hybrid modeling*. A common hybrid approach is to **combine statistical and ML models sequentially**: e.g. use a StatsForecast model to forecast the series, then feed its residual (error) into a machine learning model (like an XGBoost or an LSTM) that tries to predict the remaining error. This leverages the strength of stats models in capturing linear components and the power of ML in modeling non-linear residual patterns. For example, AutoARIMA might capture seasonality and linear trend, but its residuals might still contain patterns related to, say, non-linear technical indicator effects – a gradient boosting model can be trained on those residuals using the exogenous features. The final forecast = ARIMA forecast + ML-predicted residual.

  Another integration strategy is **concurrent modeling**: have deep learning models (perhaps via Darts which provides easy neural net models like N-BEATS, TCN, etc.) forecast alongside StatsForecast models, then either choose the best or ensemble them. Darts can facilitate ensembling by providing a common interface – you could, for instance, use Darts to produce an ensemble of an ExponentialSmoothing model and an RNN model. If using Prophet, note that Prophet can capture daily/weekly seasonality (similar to a 24h, 168h Fourier terms) and trend with possible changepoints; Prophet may serve as a complementary approach to ARIMA. **Cryptocurrency-specific nuance:** Crypto has no holidays or business days, but Prophet might still help model weekly seasonality and trend breaks. Ensure Prophet is configured with daily seasonality turned on and perhaps additional Fourier terms if needed for intraday (Prophet's default is daily & yearly seasonality for daily data; for hourly data, one can specify seasonalities manually).

* **Adaptive/Regime-based Model Choice:** Crypto markets can alternate between regimes (trending vs ranging, high vs low volatility). No single model is best for all regimes. One approach is to **monitor regime indicators** (like volatility or a trend-strength measure) and switch models accordingly. For example, if volatility is extremely high (beyond some threshold), an ARIMA might falter while a naive or volatility-scaled model might be safer. Conversely, in low volatility, a mean-reverting model might work well. A simple implementation: if a volatility regime indicator (could be an HMM state, see below, or something like VIX for crypto if it existed) indicates "crash regime", override the forecast to be closer to a naive forecast plus an appropriately wide interval, rather than trusting a model built on calmer data. This kind of conditional model selection can be thought of as an ensemble where the weighting is 100% on one model depending on state. It's advanced and requires identifying the regime in real-time (potentially via a regime-switching model as discussed later).

**Bottom line:** Use **multiple models** and **combine**. Given the 24-hour horizon, you might combine:

* A statistical model capturing seasonality (Seasonal Naive or MSTL-based forecast),
* An ARIMA capturing short-term autocorrelation,
* A machine learning model capturing non-linear relations with technical indicators.
  By ensembling these, you leverage their complementary strengths. Empirical studies on crypto forecasting often find that hybrid or ensemble models outperform any single approach. Always evaluate the ensemble against individual models to ensure it's adding value. As a tip, keep the ensemble simple (few models) to avoid overfitting and ensure you have enough data to calibrate any weighting or meta-learner.

## 4. Hyperparameter Optimization

Tuning model hyperparameters can significantly improve forecast accuracy, especially for ARIMA/ETS models and complex hybrids. We address optimizing statistical model parameters and seasonal settings:

* **Tuning ARIMA and ETS:** AutoARIMA and AutoETS in StatsForecast or pmdarima attempt to find good parameters automatically (e.g. ARIMA \$(p,d,q)(P,D,Q)\_m\$ by minimizing AIC). However, these automated choices might not be optimal for forecasting accuracy (they focus on in-sample fit). It can be useful to perform a **grid search or random search** over a range of ARIMA orders and seasonal periods, using out-of-sample error as the objective. For example, one could try \$p,q \in {0,1,2,3}\$, \$d \in {0,1}\$ (since price likely needs one difference), seasonal period \$m \in {24, 168}\$ or even include dual-season TBATS if available. Evaluate each candidate via a rolling validation and pick the best. This is computationally expensive (ARIMA fitting is \$O(n \times \text{(param combinations)})\$), but since the dataset is only \~2880 points, it's manageable with a constrained grid. **Seasonal period estimation** is particularly important: If you suspect both 24h and 168h cycles, a standard SARIMA can only incorporate one seasonal period (typically one would use \$m=24\$ for hourly data to capture daily seasonality). A workaround is to use \$m=168\$ in SARIMA (to capture weekly) and let the model's non-seasonal lags approximate the daily pattern, or vice versa. Alternatively, incorporate fourier terms or dummy variables for day-of-week to handle the second seasonality if using SARIMAX. You might try both and compare. If using StatsForecast's `AutoARIMA`, you can specify the seasonal length. For instance:

  ```python
  from statsforecast.models import AutoARIMA
  model = AutoARIMA(season_length=24, max_p=3, max_q=3, d=1, D=1)  # restrict orders
  ```

  This limits the search space. You might run two AutoARIMAs: one with `season_length=24` and one with `season_length=168`, and compare their forecast accuracy on validation. Also, ensure `D` (seasonal differencing) is considered – a weekly differencing (\$D=1\$ with \$m=168\$) might remove a weekly pattern effectively if a trend exists across weeks.

* **Bayesian Optimization:** Instead of manual grid search, **Bayesian optimization** (e.g. using `Optuna` or `hyperopt`) can find better hyperparameters more efficiently. For example, one can define a search space for ARIMA orders and have Optuna minimize the MAPE on a validation set. Each trial picks an ARIMA configuration, fits it, and evaluates. Bayesian opt will smartly explore promising regions of the space rather than brute-forcing all combos. This can also be applied to ETS (e.g. trying different error/trend/seasonal components if not using AutoETS). Keep in mind to **set a fixed random seed or use a deterministic fitting** for reproducibility when optimizing – ARIMA should be deterministic, but if your metric involves some randomness (like if you use a neural net in the loop), this matters. Also, because ARIMA fitting can fail for certain parameters (non-invertible models), make sure to catch exceptions and assign a high error for those in the optimization loop.

* **Hyperparameters for Seasonal Decomposition:** If you use MSTL or similar decomposition, there are parameters like the smoothing window and LOESS robustness iterations that can be tuned. For example, MSTL has a `window` length for each seasonal component – a larger window makes the seasonal pattern smoother (less variant over time). Crypto's daily pattern might itself evolve (e.g. perhaps the magnitude of the pattern changes in a bull vs bear market), so tuning the seasonal window length could adjust how flexible the seasonal component is. The MSTL algorithm paper provides default choices, but one could try slightly different values and verify if the decomposition residual variance decreases. This tuning is somewhat niche; more impactful is ensuring you include the right seasonal periods to begin with (which we addressed above).

* **StatsForecast Model Tuning:** StatsForecast provides other models (CES, Theta, Croston, etc.) which have their own parameters. For instance, **Theta** method or **ETS** have smoothing parameters that AutoETS estimates by maximizing likelihood. Usually, we rely on their internal optimization, but if needed, you can manually grid search smoothing parameters in ETS (level, trend, seasonality smoothing) by fixing them and evaluating – however, that's rarely necessary as the built-in optimization is sufficient. A more relevant StatsForecast tuning is using the `fallback_model` or combining models. For example, StatsForecast allows specifying a `fallback_model` if the primary fails; you can exploit this by setting a complex model with a simpler fallback. Also, if you try **croston or ADIDA** for intermittent demand (not typical for price, since price isn't intermittent), those have smoothing parameters too. In summary, focus tuning effort on ARIMA and any ML model hyperparams; ETS/Theta are either auto-optimized or less sensitive for this use case.

* **Hyperparameters in ML Models:** Though the question emphasizes StatsForecast (stats models), in a hybrid system you likely have machine learning models (like XGBoost, RNNs). It's important to tune those as well with techniques like random search or Bayesian optimization, using time-series CV. For example, tune XGBoost learning rate, tree depth, etc. or LSTM units and dropout rates. One caveat: with only 2880 training points, keep ML model complexity low; hyperparam tuning might otherwise favor overly complex models that overfit the 4-month history. Use **early stopping** and cross-validation to pick simpler models (e.g. smaller trees, fewer neurons) that generalize. If using Darts or NeuralForecast from Nixtla, they have built-in grid search support for some models or one can manually loop.

* **Seasonal Period Discovery:** If you were unsure of seasonal periods (not the case here since 24h and 168h are known candidates), one could use techniques to **discover** periodicity: examine the autocorrelation function (look for significant spikes at certain lags), or use spectral analysis (FFT or periodogram) to detect dominant frequencies. For example:

  ```python
  import numpy as np
  import scipy.signal as signal
  freqs, power = signal.periodogram(y.dropna(), fs=1)  # fs=1 hour^-1
  top_periods = 1 / freqs[np.argsort(power)[-5:]]
  ```

  This would highlight periods with high spectral power. In crypto hourly data, you'd likely see peaks corresponding to \~24 and \~168 hours if those patterns are strong. For completeness, ensure any identified period makes sense (e.g. \~23.5 hours might just be an aliasing artifact; round to 24).

**Guidance:** Hyperparameter tuning can yield marginal gains (a few percentage points improvement in MAPE) but is time-consuming. Prioritize tuning parameters that address known shortcomings: e.g., if residuals show remaining seasonality at 168h, try adding that seasonal component to ARIMA (or use MSTL). If ARIMA model is slow or overfitting, restrict \$p,q\$ ranges or use information criteria to penalize complexity (BIC tends to choose simpler models than AIC). Use automated tools (like `pmdarima.auto_arima` with `stepwise=False` for an exhaustive search, or an `Optuna` optimization loop) to systematically explore configurations. Ensure you validate on a timeframe that represents the forecasting task (e.g. recent 2 weeks to predict next day). By fine-tuning the models' parameters, you can often turn a negative \$R^2\$ model into a positive one and trim down that 22% MAPE closer to, say, 15-18%, which is more reasonable for 24h crypto forecasts. Remember though: due to the high volatility, there is a floor to forecast error – don't chase perfection; focus on robust settings that do well on average and in subperiods.

## 5. External Data Integration

Incorporating **exogenous features** like sentiment, on-chain metrics, or macroeconomic indicators can enhance crypto forecasts, as these often drive price moves beyond what past price alone can explain. The key is integrating such data properly, especially when they have different frequencies or when future values are unknown:

* **Market Sentiment and Social Media:** Bitcoin prices can be influenced by public sentiment on platforms like Twitter, Reddit, or news outlets. Studies have shown that Twitter sentiment is useful in predicting Bitcoin's price *direction* – e.g. a surge in positive tweets can foreshadow price increases. Sentiment can be quantified via sentiment analysis tools (VADER, TextBlob, or more advanced NLP models) on crypto-related tweets, producing an hourly sentiment index. Also, metrics like tweet volume or Google Trends interest for "Bitcoin" can serve as proxies for hype. To integrate, align these sentiment indices with the hourly price timestamps (e.g. sentiment at 10:00 for the hour leading to 10:00). These can be used as exogenous variables \$x\_t\$ in ARIMAX or included in the feature set for machine learning models. **Challenge:** We typically do not know future sentiment in advance – one approach is to **forecast sentiment itself** (e.g. assume mean reversion or use a separate model to predict sentiment based on time-of-day or recent trend). In practice, one might assume a neutral sentiment going forward or create scenarios ("if sentiment stays positive vs if it turns negative"). If using a deep or tree-based model, you can include past sentiment values as features to indirectly allow the model to infer how sentiment trends influence price. Another trick is using *lagged* sentiment as input to predict future price (since sentiment often leads price moves by some time). For instance, include sentiment at time \$t\$ as a feature for predicting price at \$t+1, ..., t+24\$. This way you only rely on known sentiment.

* **On-chain Data:** On-chain metrics (particularly for Bitcoin) include things like hash rate, active addresses count, transaction volume, miner revenues, etc. These reflect network usage and investor activity on the blockchain. Research indicates on-chain metrics can be **paramount for price prediction**, especially for longer horizons, but also short-term when significant shifts occur on-chain (e.g. a spike in active addresses might indicate new users buying in). For hourly forecasts, relevant on-chain data may be limited, as many on-chain metrics are daily. If you have higher-frequency on-chain data (some metrics can be obtained hourly or every block), they can be integrated similarly to sentiment – align by timestamp. If only daily on-chain metrics are available, you face a **mixed-frequency** problem: one solution is to use the last known daily value for all hours of that day (assuming intra-day on-chain changes are minor), or use interpolation. Another solution is to downsample the price to daily, model at daily frequency including on-chain, and then perhaps refine intra-day with another model. For the 24-hour ahead task, daily on-chain might suffice to indicate general direction. For example, if on-chain transaction counts have been rising for days, it might bias the forecast upward. **Handling unknown future:** Some on-chain metrics can be reasonably projected (e.g. if the hash rate trend is stable, assume tomorrow's hash rate similar to today's), but sudden changes are hard to predict. A pragmatic approach is to use **persistence** (use the latest available value as the forecast for future hours). In ARIMAX, you can set exogenous future values to the last observed value or an average. This is not ideal but is a common approach when future exogs are not known – essentially assuming "no change" in the exogenous driver, and the model will then forecast under that scenario.

* **Macroeconomic and Cross-Asset Signals:** Although Bitcoin is decentralized, it's influenced by macro factors like interest rates, inflation data, and moves in traditional markets. For example, equities (Nasdaq, S\&P500) often correlate with crypto in risk-on/risk-off sentiment; a surge in the dollar index (DXY) might inversely affect Bitcoin. You can incorporate **traditional market indices** (even if at daily frequency) as exogenous features. For instance, include the % change of Nasdaq or gold price over the last day as a feature for the crypto model. **Mixed-frequency handling:** similar to on-chain, either use the last known value for intra-day or a short-term forecast of those signals. There are specialized models like MIDAS (Mixed Data Sampling) regression that handle mixed frequencies by weighting lagged low-frequency values for high-frequency output, but a simpler way in a machine learning model is just repeating the daily value for each hour (the model will learn that it only changes once per day). For known future macro events (e.g. a Fed meeting schedule), one can incorporate a dummy variable or countdown that could hint increased volatility on those hours. Crypto-specific exogenous might also include **funding rates** (if forecasting an index price, funding rate indicates market sentiment in futures), or **stablecoin supply changes**, etc.

* **Incorporating Exogenous in Models:** In StatsForecast's `AutoARIMA` or other models, you can supply a matrix of exogenous regressors. For example, Nixtla's AutoARIMA allows an `xreg` parameter for both fitting and prediction. You would train with historical exog and pass future exog (if known or assumed) for the forecast horizon. If future exog is unknown, you might use a naive forecast as discussed. For machine learning models, you simply include those exogenous columns in your feature set (again, taking care that when predicting, you either have a way to fill those columns for the next 24 hours or you drop them if truly not available). One method is **forecasting the exogenous variables first**: for instance, predict tomorrow's sentiment or tomorrow's stock index movement (possibly using separate models or assumptions), then feed those into the crypto model. This cascades uncertainty (now two models' errors), but if the exogenous is highly predictive, it can be worth it.

* **Feature Selection among Exogenous:** Just as we did for technical indicators, apply feature importance analysis to exogenous candidates. Not all external data will help. Perhaps Twitter sentiment is helpful, but Google Trends is not, or vice versa. A mutual information test might show, for example, that *social volume* has a strong relation with BTC returns, whereas *miner revenue* does not for short horizons. Focus on a few exogenous features that add clear predictive power. The VLDB 2024 study found that using diverse data sources (technical, on-chain, sentiment, macro) *together* gave the best results, so combining different types could yield a more complete picture of market drivers.

* **Caveat – Regime Dependency:** The usefulness of exogenous data can change over time. For example, during 2017 ICO boom, on-chain usage spiked with price; in other periods, price might move on macro news regardless of on-chain activity. Be prepared to **re-evaluate** exogenous features periodically. A feature that was predictive last month may lose power if regime changes (this is another case where regime detection helps – one could decide to only trust certain exogenous signals in certain regimes).

**Implementation Tip:** Start by adding one or two exogenous features that you suspect are most impactful (say, tweet sentiment score and S\&P500 daily return) and see if forecast error improves. Ensure your modeling pipeline can handle missing future exog (perhaps by filling with last known values or by forecasting them). If using StatsForecast, structure your data frame with these exogenous columns and use the `predict(h, X=future_exog_df)` argument. If combining with deep learning, you might feed exogenous as additional input channels. Always guard against **data leakage** – if you use future information by mistake (like a future sentiment value), it will inflate performance unrealistically. Keep the process causal. With careful integration, external data can help the model anticipate moves that pure time series models would miss (for example, a large increase in positive sentiment might lead the model to predict an upward jump even if recent prices were flat).

## 6. Regime Switching Models

Crypto markets often exhibit distinct **regimes** – periods of bullish uptrend, bearish downtrend, high volatility, low volatility, etc. *Regime switching models* explicitly account for these shifts by allowing different behaviors in different states. Incorporating regime changes can improve forecasts by modeling context-dependent dynamics:

* **Hidden Markov Models (HMM):** An HMM assumes the data is generated by a process that switches between a few hidden states (regimes), each with its own statistical properties (e.g. one state could be "low volatility, modest upward drift", another "high volatility, no drift"). By fitting an HMM to Bitcoin returns or volatility, one can infer the probability of being in each regime at each time. HMMs have been successfully used to predict crypto prices, even outperforming some neural networks in studies. For example, a 2-state Gaussian HMM on BTC 1h returns might find a state 0 with small mean and low variance (stable regime) and state 1 with mean near zero but very large variance (volatile regime). Once fitted, you can use the HMM to **forecast state probabilities** for the next 24 hours and adjust predictions accordingly. If the model says there's an 80% chance we are in the volatile regime tomorrow, you might widen prediction intervals or prefer a different forecasting approach for the values. Some HMM implementations (like a Hidden Markov Model Regression) can also produce a direct forecast by weighting state-wise predictions. In Python, one can use `hmmlearn` or `pomegranate` to fit an HMM on returns:

  ```python
  from hmmlearn.hmm import GaussianHMM
  returns = df['Close'].pct_change().dropna().values.reshape(-1,1)
  hmm = GaussianHMM(n_components=2, covariance_type='diag', n_iter=100).fit(returns)
  hidden_states = hmm.predict(returns)  # inferred regime sequence
  ```

  This yields a sequence of 0/1 indicating which regime at each time. We could then examine properties of each regime (mean return, variance, duration). For forecasting, `hmm.predict` can also extrapolate one step, or we simulate future state paths. In practice, HMM could be used to **flag regimes**: e.g., if currently in state1 (high vol), maybe the forecast should rely more on a volatility model; if in state0, maybe a trend model. HMMs are unsupervised, so careful interpretation of states is needed.

* **Markov-Switching Autoregression (MS-AR):** This is a type of state-space model where the AR parameters themselves change depending on a discrete state that follows a Markov chain. Statsmodels has a `MarkovRegression` or `MarkovAutoregression` which can fit such models. For instance, a 2-regime AR(1) could have: in Regime 1, \$y\_t = \phi\_1 y\_{t-1} + \epsilon\_t\$; in Regime 2, \$y\_t = \phi\_2 y\_{t-1} + \epsilon\_t\$, with different \$\phi\$ and variance for each. This can capture, say, a mean-reverting regime vs a momentum regime. One can also allow the error variance to switch (Markov-switching GARCH is another variant). Research has shown that for crypto, regime-switching GARCH models yield better risk estimates than single-regime GARCH. To implement MS-ARIMA in Python, statsmodels' `MarkovRegression` can be used, or one can use `pystan`/`stan` for more flexible Bayesian switching models. The benefit of MS models is they do a *joint* estimation of the regimes and the ARIMA parameters. They will output probabilities of being in each regime at each time. If a certain regime has been dominant recently, you can condition forecasts on that regime. However, these models are more complex and might require more data to estimate reliably (2880 points might be borderline for a heavily parameterized MS model, but a simple 2-state AR(1) or AR(2) could be feasible).

* **Threshold Models (TAR/SETAR):** Threshold autoregressive models switch between regimes based on an observable variable crossing a threshold (instead of an unobservable Markov chain). For example, a **Self-Exciting TAR (SETAR)** could say: if the last return was above +5%, use one set of coefficients, if below -5%, use another (capturing an asymmetry in how extreme moves evolve). Or a threshold could be on the level of price: e.g. if price is above a moving average (bull regime) vs below (bear regime). In crypto, a threshold model might capture that small fluctuations mean revert, but once a price drop exceeds a threshold, a different dynamic (perhaps panic selling) kicks in. These models can be set up by splitting the data by the threshold condition and fitting separate regressions. They are simpler to conceptualize but choosing the threshold is tricky (could use domain knowledge or search methods like AIC to find an optimal threshold value).

* **Volatility Regime Models:** Instead of (or in addition to) modeling the mean of the series in regimes, one can model volatility regimes. **GARCH models** (discussed more in the next section) inherently address time-varying volatility, but a regime-switching GARCH can capture, for instance, a low-vol and high-vol regime with different GARCH parameters. An easier approach is to compute a **volatility indicator** (like a rolling std or the VIX if there was one for BTC) and classify regime by volatility level. Then incorporate that classification as an input feature (e.g. a binary feature "HighVolRegime" that is 1 if current volatility > X). This way, your ML model can use that feature to handle different regimes (essentially piecewise behavior).

* **Regime-Based Forecast Combination:** A practical way to leverage regimes is to maintain two or more models each optimized for a different regime, and then use the inferred regime to pick or weight the models. For example, you might have a model A that was trained on mostly trending periods (it might have a momentum flavor), and model B trained on ranging periods (mean-reversion flavor). If your regime detection (say an HMM) currently signals "trending", you weight forecast toward model A. This is a form of context-dependent ensemble.

**Caution:** Regime modeling adds complexity and the risk of overfitting (identifying spurious regimes). With only 120 days of data, you may only have a few regime shifts to learn from. Perhaps the data had one bullish run and one crash – an HMM might overfit by splitting into many states that don't recur. So, keep the number of regimes low (2 or 3 at most) and validate that they make intuitive sense (e.g. one state has clearly higher variance). Also, regime models often assume regimes persist for a while (the Markov chain transition matrix often has high probability of staying in the same state). Ensure that is realistic for crypto – e.g. how long do volatility clusters last?

In implementation, a **2-state HMM on returns** is a good starting point to identify "calm" vs "volatile" periods. Use that to inform either your interval scaling or which model to trust. This can help avoid the situation where a single model tries to average over two very different behaviors. By explicitly modeling regimes, you allow different dynamics to prevail in each, which can lead to better fit and forecasts – for instance, capturing that during high volatility, mean reversion might be stronger (if everyone overreacts and then price corrects) whereas in low volatility, a random walk might be fine. Overall, regime switching is an advanced but powerful tool to handle the **non-linear, non-stationary nature** of crypto markets, where "one size fits all" models struggle.

## 7. Risk Management Considerations

Forecasting cryptocurrency comes with considerable uncertainty. From a risk management perspective, one must ensure models are robust to extreme events and that forecast errors are well understood. Key considerations include:

* **Stress Testing Under Extreme Volatility:** Crypto is infamous for sudden crashes or spikes (e.g. a 30% single-day drop has happened). It's important to **stress test** your forecasting model on such scenarios to see how it behaves. This can be done by feeding in out-of-distribution inputs or simulating shock events. For example, if your model uses exogenous features, simulate a scenario where BTC price just dropped 20% in an hour and see what the 24h forecast is – does the model predict a rebound, further crash, or get extremely uncertain? If using an ARIMA or ETS, you can analytically examine how a shock propagates: ARIMA(1,0,0) for instance will carry a portion of the shock forward, etc. For ML models, you might artificially add an outlier in the test set and evaluate. The goal is to identify if the model completely breaks down or produces nonsensical predictions under stress. Some practitioners incorporate *shock features* or regime logic to handle these cases (e.g., include a binary feature that is 1 if the last return was an outlier, allowing the model to adjust specifically during those times).

* **Robustness Metrics:** Instead of relying only on MAPE or RMSE, consider **robust error metrics** that emphasize worst-case performance. For instance, **Mean Absolute Percentage Error** can blow up near zero prices – not an issue for BTC price, but keep in mind if using returns (returns can be near zero). **Quantile-based metrics** can be used: check the 90th percentile of absolute error – this tells you how bad the top 10% errors are. If your 90th percentile error is, say, 50%, that might be concerning even if MAPE is 20%. You can also track **hit rate** for directional accuracy if important for trading (what % of time the model correctly predicts up/down). Another robustness check is **rolling window error**: compute error over each week of test data. If one week the MAPE is 50%, investigate what happened (maybe an outlier event). Robust models should not completely fail even when unusual data arrives. Additionally, consider **MAE vs RMSE**: a big difference indicates large outliers (RMSE punishes large errors more). If your RMSE is much higher relative to MAE, it means occasional huge mistakes – see if you can identify and mitigate those (perhaps via outlier handling).

* **Outlier Detection and Handling:** Historical data may contain outliers (e.g. a sudden exchange glitch or one-time events like the 2020 March crash). Models like ARIMA can be distorted by outliers because they try to fit them. It's often wise to **clean or flag outliers** in the training data. Techniques include:

  * Replacing outlier values with a value closer to trend (e.g. Winsorize the returns at say the 99th percentile).
  * Adding **intervention variables** (dummy regressors) for outlier times to an ARIMAX model, so that the model can account for that blip separately. For example, if you know on a certain hour a policy change caused a spike, include a dummy =1 at that hour as an exogenous input.
  * Using **robust estimation** methods: some algorithms can fit models with reduced influence of outliers (for example, robust STL uses robust LOESS to not let outliers overly affect the trend).

  For technical indicator features, also ensure an outlier in price doesn't produce extreme indicator values that mislead the model (e.g. an outlier could cause RSI to saturate at 100 or 0; maybe cap RSI at 99 if that happens).

  Detecting outliers can be done via statistical rules (e.g. return > 5 standard deviations) or more advanced anomaly detection. Given crypto's heavy tails, you might define a return above +20% or below -20% in an hour as an outlier for modeling purposes. Once detected, decide whether to exclude, modify, or leave them. *Do not remove all large moves* though – those are precisely part of crypto behavior. The idea is to avoid fitting the normal model through these points as if they were ordinary.

* **Model Robustness to Regime Change:** We touched on regime modeling above. Even if you don't explicitly model regimes, test your model's performance in different segments: e.g. a bullish period vs a ranging period. If a model only performs well in one type, it might blow up later. A robust approach is to use **ensembles and regularization** (as discussed) to avoid over-reliance on any single assumption. Also, periodically retrain or update the model as new data comes – static models can become stale if the market structure changes (e.g. introduction of new derivatives, different participant behavior over time).

* **Risk-focused Metrics:** If using the forecasts for trading or risk management, consider metrics like **Value at Risk (VaR)** and **Expected Shortfall (ES)** on the forecast errors or returns. For example, if you generate 100 scenario forecasts for the next 24h (either via Monte Carlo or resampling residuals), you can estimate the 5% worst-case drop. Ensure the model isn't underestimating this. Robust forecasting procedures can improve VaR/ES predictions. If your model yields a distribution, calibrate that distribution so that (for instance) the 95% VaR of 24h returns predicted by the model actually covers \~95% of actual outcomes in backtests. This leads into the next section on prediction intervals.

In practice, **build defenses** around your model: incorporate sanity checks (if model predicts > +100% or < -100% change, cap it or examine), have a fallback (like a simple naive forecast) if the inputs are beyond what the model was trained on. Nixtla's StatsForecast allows a `fallback_model` which is useful: for example, if AutoARIMA fails or gives weird results, you could fallback to SeasonalNaive. Use such features to ensure you always have a reasonable forecast.

Finally, treat the model as one tool in a risk management framework. Even the best model will have large errors at times – plan for that. Use prediction intervals, monitor model error in real-time (if the model starts performing significantly worse, it may need retraining or re-specification), and don't rely solely on point forecasts. By accounting for extreme scenarios and making the model as robust as possible, you reduce the chance of being caught off guard by crypto's wild moves.

## 8. Prediction Interval Calibration

Point forecasts are incomplete without an estimate of uncertainty. Especially in crypto, **prediction intervals (PIs)** are crucial to understand the range of possible outcomes and to make risk-aware decisions. Here we discuss how to obtain and calibrate prediction intervals, and handle heteroscedasticity (changing volatility):

* **Parametric Prediction Intervals (Constant Variance):** Traditional statistical models like ARIMA and ETS can provide analytical prediction intervals assuming errors are normally distributed with constant variance. For example, StatsForecast's `predict` can return intervals for specified confidence levels. These are typically based on the in-sample sigma of residuals. For instance, an ARIMA will produce forecast \$\hat{y}*{t+h}\$ and an associated standard error \$\hat{\sigma}*{h}\$, and an X% interval is \$\hat{y}\pm z\_{X/2}\hat{\sigma}\$. In code with StatsForecast:

  ```python
  # Generate 90% prediction interval for next 24 points
  forecast_df = sf.predict(h=24, level=[90])
  print(forecast_df.columns)  # contains 'y' (forecast), 'lo-90', 'hi-90'
  ```

  This yields upper and lower bounds (lo-90, hi-90) for each forecast step. However, these **assume homoscedasticity and normally distributed errors**. Crypto returns often violate both: error variance is not constant (volatility clustering) and distribution has heavy tails (leptokurtic, outlier-prone). As a result, the true coverage of these intervals may be much lower than nominal (e.g. a 95% ARIMA interval might only contain the actual outcome 80% of the time if volatility spikes or tail events occur). Still, as a baseline, compute these intervals and then assess calibration on a validation set: count how often actual price fell outside the 95% band. If too often, the intervals are too narrow (underestimating risk).

* **Heteroscedastic Models (GARCH):** To account for volatility changes, integrate a **GARCH (Generalized Autoregressive Conditional Heteroscedasticity)** model for the residuals or returns. A GARCH(1,1) model, for example, will update the forecast variance each step based on the previous error and variance. One approach is to use a two-step model: use ARIMA to forecast the mean, and GARCH on the residuals to forecast the variance. The combination is sometimes called ARIMA+GARCH or a "volatility forecast". For instance:

  ```python
  from arch import arch_model
  # Assume we have residuals from ARIMA
  am = arch_model(residuals, p=1, q=1)  # GARCH(1,1)
  res = am.fit(disp='off')
  # Forecast volatility for next 24 hours
  vol_forecast = res.forecast(horizon=24)
  sigma_pred = vol_forecast.variance.values[-1]**0.5  # array of sigma for each horizon
  ```

  If \$m\_t\$ is ARIMA forecast and \$\sigma\_t\$ from GARCH, a 95% interval for next step could be \$m\_{t+1}\pm 1.96\sigma\_{t+1}\$. This way, if volatility is currently high, \$\sigma\_{t+1}\$ will be high, widening the interval appropriately. Markov-Switching GARCH can further handle abrupt shifts in volatility regimes, but even a standard GARCH is helpful. GARCH models often assume a certain distribution for errors (normal or Student-t). Using a heavy-tailed distribution (Student-t) in GARCH can better capture extreme moves, giving wider intervals for a given variance if needed (fat tails increase interval size for same variance to achieve desired coverage).

* **Quantile Regression and Machine Learning PIs:** Another way to get intervals is to directly model quantiles of the future price. For example, train a model to predict the 0.05 (5th percentile) and 0.95 (95th percentile) of the 24h ahead return. **Quantile regression** can be done with linear models or tree-based models (e.g. Gradient Boosting or Random Forest can predict quantiles by minimizing quantile loss). The *LightGBM* library supports quantile objectives, or one can use an algorithm like **NGBoost** (Natural Gradient Boosting) which outputs a full predictive distribution rather than a point. NGBoost, for example, might fit a Normal distribution to the 24h return with a mean and variance that are functions of input features. This inherently gives a prediction interval (e.g. mean ± 1.96\*std if assuming normal, but it can also predict a t-distribution for heavier tails). The advantage of these approaches is they can incorporate heteroscedasticity by learning to output larger variance when features (like recent volatility or volume) indicate so.

  Another advanced ML approach is **Quantile Random Forest**, which can derive quantiles from the distribution of trees' predictions. And recently, models like *Deep Ensembles* or *Monte Carlo dropout* in neural nets are used to get uncertainty. For example, if using an LSTM, you could train an ensemble of LSTMs or use dropout at prediction time to simulate a distribution of outcomes, then take quantiles.

* **Prediction Interval Calibration:** Regardless of how you obtain intervals, you should calibrate them to ensure the intended coverage. Calibration means that the proportion of times the actual value falls within the X% interval is close to X%. You can check calibration by backtesting: e.g., over the last 30 days of 24h forecasts, did roughly 95% of actual prices fall inside the 95% interval? If not, adjust. Adjustments could be:

  * *Scaling the variance*: If intervals are too narrow (undercovering), you might multiply the predicted \$\sigma\$ by a factor >1 until coverage is met. This is like saying the model underestimates uncertainty by a constant factor.
  * *Using a heavier-tailed distribution*: If using normal assumption yields undercoverage due to outliers, using a Student-t with \$\nu\$ degrees of freedom can widen tails. You might fit \$\nu\$ as well by MLE or cross-val. For example, if you find that extreme errors happen more often than normal, a t-distribution with df=4 or 5 might match the empirical error distribution better, leading to wider intervals.
  * *Differentiating by regime*: In high-vol regimes, even a GARCH might underpredict if an unprecedented event occurs. You may set a minimum width or add an extra cushion in turbulent periods. For instance, during a detected regime of extreme volatility, inflate the interval by some percentage.

* **NGBoost and Advanced Probabilistic Models:** NGBoost deserves a note as it often performs well on **small datasets for probabilistic regression**. With \~2880 points, NGBoost (which is based on boosting) can be effective, and it inherently gives you a predictive distribution (you can choose Normal, Laplace, etc.). The NGBoost paper showed strong calibration and accuracy on various datasets, and it's relatively easy to implement. Similarly, consider **Prophet's intervals** – Prophet can generate intervals by Monte Carlo sampling of its Bayesian posterior, but Prophet's defaults assume normal error too (or student t if specified). If you use Prophet in ensemble, you can leverage its interval as one input.

* **Empirical Prediction Intervals:** Another technique is to generate **empirical forecasts** through simulation or bootstrapping. For example, you could take your residual series, bootstrap sample blocks of residuals (to preserve autocorrelation), and add them to your forecast path to generate many sample price paths for the next 24 hours (this is a *bootstrap PI* approach). The quantiles of those simulated paths form intervals. This method makes minimal distributional assumptions and can naturally incorporate patterns in residuals. The StatsForecast MSTL model by default forecasts each component separately and could generate probabilistic forecasts if the trend forecaster (like ARIMA) provides them, combined with naive seasonality (which can also have uncertainty if you simulate seasonality errors).

In practice, for each 24h forecast, you would output not just a point estimate but a band (or multiple levels of bands, e.g. 80% and 95%). Given crypto's volatility, these bands will be quite wide. It's not unusual to see a 95% interval that spans ±10% or more of the price. Communicate that this is expected given the unpredictable nature, rather than a failure of the model. **Backtest the interval coverage**: if you target 90% interval, about 9 out of 10 actual outcomes should fall in it. If not, refine the approach (maybe your GARCH needs a higher order, or your ML model needs to incorporate a volatility feature to widen intervals on volatile days).

Finally, remember that prediction intervals **assume no structural break** beyond what's modeled. If something truly out-of-distribution happens (exchange hack, black swan event), all bets are off – the interval will likely not cover it. That's where stress testing and adding a safety margin come in. Nonetheless, by using the above methods, you can at least capture the usual uncertainty and volatility in crypto forecasting and provide interval forecasts that are **dynamically sized** – narrow during stable periods and wider during volatile periods – yielding more realistic predictions. This helps in making informed decisions (e.g. position sizing in trading or setting stop-loss levels) based on not just a single-point forecast but a spectrum of possible outcomes.

## Conclusion

In this report, we explored a range of advanced techniques to improve short-term (24h ahead) forecasts for BTC/USDT hourly prices within a hybrid stat+ML framework. To summarize:

* We can better capture **multiple seasonalities** (24h, 168h) and **non-linear trend** via decomposition (STL/MSTL), allowing models to focus on the residual signal.
* Through **feature engineering**, including lags, rolling metrics, and carefully selected technical indicators (using PCA, mutual information, Boruta), we enrich the model inputs while avoiding multicollinearity.
* By employing **ensembles and hybrid models**, we leverage the strengths of different methods – statistical models, Prophet, deep learning – to improve forecast accuracy and robustness.
* Systematic **hyperparameter tuning** (via grid search or Bayesian optimization) helps tailor ARIMA/ETS to crypto's patterns and identifies the proper seasonal settings.
* **External data** (sentiment, on-chain, macro) can provide leading information, but must be integrated with care (forecast or assume future exog values, handle mixed frequencies).
* **Regime switching models** (HMM, Markov-switching AR/GARCH, threshold models) address the market's changing dynamics, enabling different predictive behavior in bull vs bear or high vs low volatility regimes.
* We enforced **risk management** by stress-testing extreme scenarios, using robust metrics, and handling outliers, ensuring the model remains reliable during wild market swings.
* Finally, we focused on **prediction interval calibration**, using GARCH for heteroscedasticity and quantile methods (like NGBoost) for probabilistic forecasting, so that our confidence intervals truly reflect real-world uncertainty.

By applying these techniques, a practitioner can significantly improve the performance and trustworthiness of a crypto forecasting system. Expect iterative improvement: apply decomposition and feature selection, retrain models, combine their forecasts, tune hyperparams, and check performance. Over time, the model's MAPE should decrease (potentially into the teens) and \$R^2\$ become positive, indicating it outperforms naive predictions. More importantly, the model will be **robust** – aware of seasonality, adaptive to volatility, informed by external signals, and honest about its own uncertainty. In the volatile realm of cryptocurrency, such a multifaceted approach is essential to produce reliable short-term forecasts. Each technique discussed comes with computational overhead and complexity, but with 120 days of data and modern libraries (StatsForecast, scikit-learn, etc.), they are feasible to implement. The result will be a hybrid model that not only predicts the most likely price trajectory but also provides insight into the range of possibilities, helping users make better decisions in the face of crypto market uncertainty.