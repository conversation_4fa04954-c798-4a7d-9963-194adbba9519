#!/usr/bin/env python
# coding: utf-8

"""
# Time series: A simple moving average (MA) model

## Metadata
title: Moving Average Models for Time Series Analysis
type: example
tags: time-series, moving-average, ARIMA, statsmodels, stock-market
related: 
  - forecasting/techniques/decomposition_time-series-example
  - forecasting/models/lstm_sine-wave-example
models: ARIMA, moving-average
techniques: time-series-forecasting, rolling-window, moving-average
datasets: stock-market-IBEX35
complexity: beginner

## Summary
This example demonstrates how to implement both a simple rolling average and a 
proper moving-average (MA) model using the ARIMA class from statsmodels. 
The example uses IBEX35 stock market data to compare these approaches for
time series smoothing and basic prediction.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.pylab import rcParams
rcParams['figure.figsize'] = 15, 5

# Install yfinance package for fetching stock data
# !pip -q install yfinance
import yfinance as yf

# For our data we shall use the '**Close**' value from the IBEX35 stock market.
# 
# (Note that stock data is pretty much a random walk, and is extremely difficult to predict. For example see the notebook ["*LSTM time series + stock price prediction = FAIL*"](https://www.kaggle.com/carlmcbrideellis/lstm-time-series-stock-price-prediction-fail)).


IBEX = yf.Ticker("^IBEX")
# get historical market data
IBEX_values = IBEX.history(start="2020-06-01")

# let us take a look


IBEX_values[['Close']].plot(lw=2);

# ### Rolling average
# We shall now create a rolling average, with a window size of 10, using [`pandas.DataFrame.rolling`](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.rolling.html)


IBEX_values['rolling_av'] = IBEX_values['Close'].rolling(10).mean()
# take a look
IBEX_values[['Close','rolling_av']].plot(lw=2);

# ### Moving average model
# To create our moving average model we shall use [`ARIMA`](https://www.statsmodels.org/stable/generated/statsmodels.tsa.arima.model.ARIMA.html) from [statsmodels](https://www.statsmodels.org/). This is a composite of an autoregressive model, **AR**($p$), an integration model, **I**($d$), and a moving average model, **MA**($q$), which is passed via the tuple `order(p,d,q)`. Here we shall use again a window of $q=10$:


from statsmodels.tsa.arima.model import ARIMA
ARMA_model = ARIMA(endog=IBEX_values['Close'], order=(0, 0, 10))
results = ARMA_model.fit()
print(results.summary())

# ## Predictions
# We shall now look at the predictions made by our moving average model


start_date = '2020-06-12'
end_date   = '2021-06-04'
IBEX_values['forecast'] = results.predict(start=start_date, end=end_date)

# take a look at the numbers


IBEX_values[['Close','rolling_av','forecast']].tail(10)

# and now plot


IBEX_values[['Close','rolling_av','forecast']].plot(lw=2);